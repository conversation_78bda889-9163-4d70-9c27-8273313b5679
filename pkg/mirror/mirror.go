// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Package mirror provides configurable mirror support for buildpack downloads.
// This allows users to specify alternative download sources for language runtimes
// and package manager repositories, particularly useful in regions with connectivity
// issues to default upstream sources.
package mirror

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	gcp "github.com/GoogleCloudPlatform/buildpacks/pkg/gcpbuildpack"
)

// Environment variable names for mirror configuration
const (
	// MirrorConfigFile specifies path to a JSON configuration file containing mirror URLs
	MirrorConfigFile = "GOOGLE_MIRROR_CONFIG_FILE"
	
	// Runtime mirror environment variables
	RuntimeMirrorBase     = "GOOGLE_RUNTIME_MIRROR_BASE_URL"
	NodejsRuntimeMirror   = "GOOGLE_NODEJS_RUNTIME_MIRROR"
	PythonRuntimeMirror   = "GOOGLE_PYTHON_RUNTIME_MIRROR"
	JavaRuntimeMirror     = "GOOGLE_JAVA_RUNTIME_MIRROR"
	GoRuntimeMirror       = "GOOGLE_GO_RUNTIME_MIRROR"
	RubyRuntimeMirror     = "GOOGLE_RUBY_RUNTIME_MIRROR"
	PHPRuntimeMirror      = "GOOGLE_PHP_RUNTIME_MIRROR"
	DotnetRuntimeMirror   = "GOOGLE_DOTNET_RUNTIME_MIRROR"
	DartRuntimeMirror     = "GOOGLE_DART_RUNTIME_MIRROR"
	FlutterRuntimeMirror  = "GOOGLE_FLUTTER_RUNTIME_MIRROR"
	
	// Package manager mirror environment variables
	NPMRegistryMirror     = "GOOGLE_NPM_REGISTRY_MIRROR"
	YarnRegistryMirror    = "GOOGLE_YARN_REGISTRY_MIRROR"
	PipIndexMirror        = "GOOGLE_PIP_INDEX_MIRROR"
	MavenCentralMirror    = "GOOGLE_MAVEN_CENTRAL_MIRROR"
	ComposerMirror        = "GOOGLE_COMPOSER_MIRROR"
	RubygemsMirror        = "GOOGLE_RUBYGEMS_MIRROR"
	PubDevMirror          = "GOOGLE_PUB_DEV_MIRROR"
	NugetMirror           = "GOOGLE_NUGET_MIRROR"
)

// Default upstream URLs
const (
	DefaultGoogleRuntimeBase = "https://dl.google.com/runtimes"
	DefaultGoRuntimeBase     = "https://dl.google.com/go"
	DefaultDartSDKBase       = "https://storage.googleapis.com/dart-archive/channels/stable/release"
	DefaultFlutterSDKBase    = "https://storage.googleapis.com/flutter_infra_release/releases"
	DefaultNPMRegistry       = "https://registry.npmjs.org"
	DefaultYarnRegistry      = "https://repo.yarnpkg.com"
	DefaultPipIndex          = "https://pypi.org/simple"
	DefaultMavenCentral      = "https://repo1.maven.org/maven2"
	DefaultComposer          = "https://packagist.org"
	DefaultRubygems          = "https://rubygems.org"
	DefaultPubDev            = "https://pub.dev"
	DefaultNuget             = "https://api.nuget.org/v3-flatcontainer"
)

// MirrorConfig represents the complete mirror configuration
type MirrorConfig struct {
	RuntimeMirrors struct {
		BaseURL string `json:"base_url,omitempty"`
		Nodejs  string `json:"nodejs,omitempty"`
		Python  string `json:"python,omitempty"`
		Java    string `json:"java,omitempty"`
		Go      string `json:"go,omitempty"`
		Ruby    string `json:"ruby,omitempty"`
		PHP     string `json:"php,omitempty"`
		Dotnet  string `json:"dotnet,omitempty"`
		Dart    string `json:"dart,omitempty"`
		Flutter string `json:"flutter,omitempty"`
	} `json:"runtime_mirrors"`
	
	PackageManagerMirrors struct {
		NPMRegistry   string `json:"npm_registry,omitempty"`
		YarnRegistry  string `json:"yarn_registry,omitempty"`
		PipIndex      string `json:"pip_index,omitempty"`
		MavenCentral  string `json:"maven_central,omitempty"`
		Composer      string `json:"composer,omitempty"`
		Rubygems      string `json:"rubygems,omitempty"`
		PubDev        string `json:"pub_dev,omitempty"`
		Nuget         string `json:"nuget,omitempty"`
	} `json:"package_manager_mirrors"`
}

// LoadMirrorConfig loads mirror configuration from environment variables and config file
func LoadMirrorConfig(ctx *gcp.Context) (*MirrorConfig, error) {
	config := &MirrorConfig{}
	
	// Load from config file if specified
	if configFile := os.Getenv(MirrorConfigFile); configFile != "" {
		if err := loadConfigFromFile(ctx, configFile, config); err != nil {
			ctx.Warnf("Failed to load mirror config from file %s: %v", configFile, err)
		}
	}
	
	// Override with environment variables (they take precedence)
	loadConfigFromEnv(config)
	
	return config, nil
}

// loadConfigFromFile loads configuration from a JSON file
func loadConfigFromFile(ctx *gcp.Context, configFile string, config *MirrorConfig) error {
	// Support both absolute and relative paths
	var filePath string
	if filepath.IsAbs(configFile) {
		filePath = configFile
	} else {
		filePath = filepath.Join(ctx.ApplicationRoot(), configFile)
	}
	
	exists, err := ctx.FileExists(filePath)
	if err != nil {
		return fmt.Errorf("checking config file existence: %w", err)
	}
	if !exists {
		return fmt.Errorf("config file not found: %s", filePath)
	}
	
	data, err := ctx.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("reading config file: %w", err)
	}
	
	if err := json.Unmarshal([]byte(data), config); err != nil {
		return fmt.Errorf("parsing config file JSON: %w", err)
	}
	
	return nil
}

// loadConfigFromEnv loads configuration from environment variables
func loadConfigFromEnv(config *MirrorConfig) {
	// Runtime mirrors
	if url := os.Getenv(RuntimeMirrorBase); url != "" {
		config.RuntimeMirrors.BaseURL = url
	}
	if url := os.Getenv(NodejsRuntimeMirror); url != "" {
		config.RuntimeMirrors.Nodejs = url
	}
	if url := os.Getenv(PythonRuntimeMirror); url != "" {
		config.RuntimeMirrors.Python = url
	}
	if url := os.Getenv(JavaRuntimeMirror); url != "" {
		config.RuntimeMirrors.Java = url
	}
	if url := os.Getenv(GoRuntimeMirror); url != "" {
		config.RuntimeMirrors.Go = url
	}
	if url := os.Getenv(RubyRuntimeMirror); url != "" {
		config.RuntimeMirrors.Ruby = url
	}
	if url := os.Getenv(PHPRuntimeMirror); url != "" {
		config.RuntimeMirrors.PHP = url
	}
	if url := os.Getenv(DotnetRuntimeMirror); url != "" {
		config.RuntimeMirrors.Dotnet = url
	}
	if url := os.Getenv(DartRuntimeMirror); url != "" {
		config.RuntimeMirrors.Dart = url
	}
	if url := os.Getenv(FlutterRuntimeMirror); url != "" {
		config.RuntimeMirrors.Flutter = url
	}
	
	// Package manager mirrors
	if url := os.Getenv(NPMRegistryMirror); url != "" {
		config.PackageManagerMirrors.NPMRegistry = url
	}
	if url := os.Getenv(YarnRegistryMirror); url != "" {
		config.PackageManagerMirrors.YarnRegistry = url
	}
	if url := os.Getenv(PipIndexMirror); url != "" {
		config.PackageManagerMirrors.PipIndex = url
	}
	if url := os.Getenv(MavenCentralMirror); url != "" {
		config.PackageManagerMirrors.MavenCentral = url
	}
	if url := os.Getenv(ComposerMirror); url != "" {
		config.PackageManagerMirrors.Composer = url
	}
	if url := os.Getenv(RubygemsMirror); url != "" {
		config.PackageManagerMirrors.Rubygems = url
	}
	if url := os.Getenv(PubDevMirror); url != "" {
		config.PackageManagerMirrors.PubDev = url
	}
	if url := os.Getenv(NugetMirror); url != "" {
		config.PackageManagerMirrors.Nuget = url
	}
}

// GetRuntimeMirrorURL returns the mirror URL for a specific runtime, falling back to defaults
func (c *MirrorConfig) GetRuntimeMirrorURL(runtime string) string {
	switch strings.ToLower(runtime) {
	case "nodejs":
		if c.RuntimeMirrors.Nodejs != "" {
			return c.RuntimeMirrors.Nodejs
		}
	case "python":
		if c.RuntimeMirrors.Python != "" {
			return c.RuntimeMirrors.Python
		}
	case "java", "openjdk", "canonicaljdk":
		if c.RuntimeMirrors.Java != "" {
			return c.RuntimeMirrors.Java
		}
	case "go":
		if c.RuntimeMirrors.Go != "" {
			return c.RuntimeMirrors.Go
		}
		return DefaultGoRuntimeBase
	case "ruby":
		if c.RuntimeMirrors.Ruby != "" {
			return c.RuntimeMirrors.Ruby
		}
	case "php":
		if c.RuntimeMirrors.PHP != "" {
			return c.RuntimeMirrors.PHP
		}
	case "dotnet", "dotnetsdk", "aspnetcore":
		if c.RuntimeMirrors.Dotnet != "" {
			return c.RuntimeMirrors.Dotnet
		}
	case "dart":
		if c.RuntimeMirrors.Dart != "" {
			return c.RuntimeMirrors.Dart
		}
		return DefaultDartSDKBase
	case "flutter":
		if c.RuntimeMirrors.Flutter != "" {
			return c.RuntimeMirrors.Flutter
		}
		return DefaultFlutterSDKBase
	}
	
	// Use base URL if specific runtime mirror not configured
	if c.RuntimeMirrors.BaseURL != "" {
		return c.RuntimeMirrors.BaseURL
	}
	
	return DefaultGoogleRuntimeBase
}

// GetPackageManagerMirrorURL returns the mirror URL for a specific package manager
func (c *MirrorConfig) GetPackageManagerMirrorURL(packageManager string) string {
	switch strings.ToLower(packageManager) {
	case "npm":
		if c.PackageManagerMirrors.NPMRegistry != "" {
			return c.PackageManagerMirrors.NPMRegistry
		}
		return DefaultNPMRegistry
	case "yarn":
		if c.PackageManagerMirrors.YarnRegistry != "" {
			return c.PackageManagerMirrors.YarnRegistry
		}
		return DefaultYarnRegistry
	case "pip":
		if c.PackageManagerMirrors.PipIndex != "" {
			return c.PackageManagerMirrors.PipIndex
		}
		return DefaultPipIndex
	case "maven":
		if c.PackageManagerMirrors.MavenCentral != "" {
			return c.PackageManagerMirrors.MavenCentral
		}
		return DefaultMavenCentral
	case "composer":
		if c.PackageManagerMirrors.Composer != "" {
			return c.PackageManagerMirrors.Composer
		}
		return DefaultComposer
	case "rubygems":
		if c.PackageManagerMirrors.Rubygems != "" {
			return c.PackageManagerMirrors.Rubygems
		}
		return DefaultRubygems
	case "pub":
		if c.PackageManagerMirrors.PubDev != "" {
			return c.PackageManagerMirrors.PubDev
		}
		return DefaultPubDev
	case "nuget":
		if c.PackageManagerMirrors.Nuget != "" {
			return c.PackageManagerMirrors.Nuget
		}
		return DefaultNuget
	default:
		return ""
	}
}

// LogMirrorConfiguration logs the active mirror configuration for debugging
func (c *MirrorConfig) LogMirrorConfiguration(ctx *gcp.Context) {
	if ctx.Debug() {
		ctx.Debugf("Mirror configuration loaded:")
		
		// Log runtime mirrors
		if c.RuntimeMirrors.BaseURL != "" {
			ctx.Debugf("  Runtime base URL: %s", c.RuntimeMirrors.BaseURL)
		}
		if c.RuntimeMirrors.Nodejs != "" {
			ctx.Debugf("  Node.js runtime: %s", c.RuntimeMirrors.Nodejs)
		}
		if c.RuntimeMirrors.Python != "" {
			ctx.Debugf("  Python runtime: %s", c.RuntimeMirrors.Python)
		}
		if c.RuntimeMirrors.Java != "" {
			ctx.Debugf("  Java runtime: %s", c.RuntimeMirrors.Java)
		}
		if c.RuntimeMirrors.Go != "" {
			ctx.Debugf("  Go runtime: %s", c.RuntimeMirrors.Go)
		}
		
		// Log package manager mirrors
		if c.PackageManagerMirrors.NPMRegistry != "" {
			ctx.Debugf("  NPM registry: %s", c.PackageManagerMirrors.NPMRegistry)
		}
		if c.PackageManagerMirrors.PipIndex != "" {
			ctx.Debugf("  Pip index: %s", c.PackageManagerMirrors.PipIndex)
		}
		if c.PackageManagerMirrors.MavenCentral != "" {
			ctx.Debugf("  Maven Central: %s", c.PackageManagerMirrors.MavenCentral)
		}
	}
}
