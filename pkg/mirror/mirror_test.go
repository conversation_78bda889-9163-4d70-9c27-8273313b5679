// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package mirror

import (
	"os"
	"path/filepath"
	"testing"

	gcp "github.com/GoogleCloudPlatform/buildpacks/pkg/gcpbuildpack"
)

func TestLoadMirrorConfig_FromEnv(t *testing.T) {
	// Set up environment variables
	os.Setenv(RuntimeMirrorBase, "https://mirrors.example.com/runtimes")
	os.Setenv(NodejsRuntimeMirror, "https://mirrors.example.com/nodejs")
	os.Setenv(NPMRegistryMirror, "https://registry.npm.taobao.org")
	defer func() {
		os.Unsetenv(RuntimeMirrorBase)
		os.Unsetenv(NodejsRuntimeMirror)
		os.Unsetenv(NPMRegistryMirror)
	}()

	ctx := gcp.NewContext()
	config, err := LoadMirrorConfig(ctx)
	if err != nil {
		t.Fatalf("LoadMirrorConfig() failed: %v", err)
	}

	if config.RuntimeMirrors.BaseURL != "https://mirrors.example.com/runtimes" {
		t.Errorf("Expected base URL to be 'https://mirrors.example.com/runtimes', got '%s'", config.RuntimeMirrors.BaseURL)
	}

	if config.RuntimeMirrors.Nodejs != "https://mirrors.example.com/nodejs" {
		t.Errorf("Expected Node.js mirror to be 'https://mirrors.example.com/nodejs', got '%s'", config.RuntimeMirrors.Nodejs)
	}

	if config.PackageManagerMirrors.NPMRegistry != "https://registry.npm.taobao.org" {
		t.Errorf("Expected NPM registry to be 'https://registry.npm.taobao.org', got '%s'", config.PackageManagerMirrors.NPMRegistry)
	}
}

func TestLoadMirrorConfig_FromFile(t *testing.T) {
	// Create a temporary config file
	tempDir := t.TempDir()
	configFile := filepath.Join(tempDir, "mirror-config.json")
	configContent := `{
		"runtime_mirrors": {
			"base_url": "https://mirrors.example.com/runtimes",
			"nodejs": "https://mirrors.example.com/nodejs",
			"python": "https://mirrors.example.com/python"
		},
		"package_manager_mirrors": {
			"npm_registry": "https://registry.npm.taobao.org",
			"pip_index": "https://pypi.tuna.tsinghua.edu.cn/simple"
		}
	}`

	if err := os.WriteFile(configFile, []byte(configContent), 0644); err != nil {
		t.Fatalf("Failed to write config file: %v", err)
	}

	os.Setenv(MirrorConfigFile, configFile)
	defer os.Unsetenv(MirrorConfigFile)

	ctx := gcp.NewContext(gcp.WithApplicationRoot(tempDir))
	config, err := LoadMirrorConfig(ctx)
	if err != nil {
		t.Fatalf("LoadMirrorConfig() failed: %v", err)
	}

	if config.RuntimeMirrors.BaseURL != "https://mirrors.example.com/runtimes" {
		t.Errorf("Expected base URL to be 'https://mirrors.example.com/runtimes', got '%s'", config.RuntimeMirrors.BaseURL)
	}

	if config.RuntimeMirrors.Python != "https://mirrors.example.com/python" {
		t.Errorf("Expected Python mirror to be 'https://mirrors.example.com/python', got '%s'", config.RuntimeMirrors.Python)
	}

	if config.PackageManagerMirrors.PipIndex != "https://pypi.tuna.tsinghua.edu.cn/simple" {
		t.Errorf("Expected Pip index to be 'https://pypi.tuna.tsinghua.edu.cn/simple', got '%s'", config.PackageManagerMirrors.PipIndex)
	}
}

func TestGetRuntimeMirrorURL(t *testing.T) {
	config := &MirrorConfig{}
	config.RuntimeMirrors.BaseURL = "https://mirrors.example.com/runtimes"
	config.RuntimeMirrors.Nodejs = "https://mirrors.example.com/nodejs"
	config.RuntimeMirrors.Go = "https://mirrors.example.com/go"

	tests := []struct {
		runtime  string
		expected string
	}{
		{"nodejs", "https://mirrors.example.com/nodejs"},
		{"go", "https://mirrors.example.com/go"},
		{"python", "https://mirrors.example.com/runtimes"}, // Falls back to base URL
		{"unknown", "https://mirrors.example.com/runtimes"}, // Falls back to base URL
	}

	for _, test := range tests {
		result := config.GetRuntimeMirrorURL(test.runtime)
		if result != test.expected {
			t.Errorf("GetRuntimeMirrorURL(%s) = %s, expected %s", test.runtime, result, test.expected)
		}
	}
}

func TestGetRuntimeMirrorURL_Defaults(t *testing.T) {
	config := &MirrorConfig{} // Empty config

	tests := []struct {
		runtime  string
		expected string
	}{
		{"go", DefaultGoRuntimeBase},
		{"dart", DefaultDartSDKBase},
		{"flutter", DefaultFlutterSDKBase},
		{"nodejs", DefaultGoogleRuntimeBase}, // Falls back to default
		{"python", DefaultGoogleRuntimeBase}, // Falls back to default
	}

	for _, test := range tests {
		result := config.GetRuntimeMirrorURL(test.runtime)
		if result != test.expected {
			t.Errorf("GetRuntimeMirrorURL(%s) = %s, expected %s", test.runtime, result, test.expected)
		}
	}
}

func TestGetPackageManagerMirrorURL(t *testing.T) {
	config := &MirrorConfig{}
	config.PackageManagerMirrors.NPMRegistry = "https://registry.npm.taobao.org"
	config.PackageManagerMirrors.PipIndex = "https://pypi.tuna.tsinghua.edu.cn/simple"

	tests := []struct {
		packageManager string
		expected       string
	}{
		{"npm", "https://registry.npm.taobao.org"},
		{"pip", "https://pypi.tuna.tsinghua.edu.cn/simple"},
		{"maven", DefaultMavenCentral}, // Falls back to default
		{"unknown", ""},               // Returns empty for unknown
	}

	for _, test := range tests {
		result := config.GetPackageManagerMirrorURL(test.packageManager)
		if result != test.expected {
			t.Errorf("GetPackageManagerMirrorURL(%s) = %s, expected %s", test.packageManager, result, test.expected)
		}
	}
}

func TestEnvOverridesFile(t *testing.T) {
	// Create a temporary config file
	tempDir := t.TempDir()
	configFile := filepath.Join(tempDir, "mirror-config.json")
	configContent := `{
		"runtime_mirrors": {
			"nodejs": "https://file.example.com/nodejs"
		},
		"package_manager_mirrors": {
			"npm_registry": "https://file.example.com/npm"
		}
	}`

	if err := os.WriteFile(configFile, []byte(configContent), 0644); err != nil {
		t.Fatalf("Failed to write config file: %v", err)
	}

	// Set environment variables that should override file config
	os.Setenv(MirrorConfigFile, configFile)
	os.Setenv(NodejsRuntimeMirror, "https://env.example.com/nodejs")
	os.Setenv(NPMRegistryMirror, "https://env.example.com/npm")
	defer func() {
		os.Unsetenv(MirrorConfigFile)
		os.Unsetenv(NodejsRuntimeMirror)
		os.Unsetenv(NPMRegistryMirror)
	}()

	ctx := gcp.NewContext(gcp.WithApplicationRoot(tempDir))
	config, err := LoadMirrorConfig(ctx)
	if err != nil {
		t.Fatalf("LoadMirrorConfig() failed: %v", err)
	}

	// Environment variables should override file config
	if config.RuntimeMirrors.Nodejs != "https://env.example.com/nodejs" {
		t.Errorf("Expected Node.js mirror to be 'https://env.example.com/nodejs', got '%s'", config.RuntimeMirrors.Nodejs)
	}

	if config.PackageManagerMirrors.NPMRegistry != "https://env.example.com/npm" {
		t.Errorf("Expected NPM registry to be 'https://env.example.com/npm', got '%s'", config.PackageManagerMirrors.NPMRegistry)
	}
}
