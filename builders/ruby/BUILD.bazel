load("//tools:defs.bzl", "builder")

package(default_visibility = ["//builders/ruby:__subpackages__"])

licenses(["notice"])

exports_files([
    "builder.toml.template",
])

BUILDPACKS = [
    "//cmd/config/entrypoint:entrypoint.tgz",
    "//cmd/config/flex:flex.tgz",
    "//cmd/ruby/missing_entrypoint:missing_entrypoint.tgz",
    "//cmd/ruby/appengine_validation:appengine_validation.tgz",
    "//cmd/ruby/appengine:appengine.tgz",
    "//cmd/ruby/flex_entrypoint:flex_entrypoint.tgz",
    "//cmd/ruby/rubygems:rubygems.tgz",
    "//cmd/ruby/bundle:bundle.tgz",
    "//cmd/ruby/rails:rails.tgz",
    "//cmd/ruby/runtime:runtime.tgz",
    "//cmd/utils/label:label_image.tgz",
    "//cmd/ruby/functions_framework:functions_framework.tgz",
    "//cmd/utils/archive_source:archive_source.tgz",
]

GROUPS = {
    "nodejs": [
        "//cmd/nodejs/runtime:runtime.tgz",
        "//cmd/nodejs/yarn:yarn.tgz",
    ],
}

builder(
    name = "builder",
    builder_template = ":builder.toml.template",
    buildpacks = BUILDPACKS,
    descriptor = "builder.toml",
    groups = GROUPS,
    image = "gcp/ruby",
    stack = "google.gae.18",
)

builder(
    name = "builder_22",
    builder_template = ":builder.toml.template",
    buildpacks = BUILDPACKS,
    descriptor = "builder.toml",
    groups = GROUPS,
    image = "gcp/ruby-22",
    stack = "google.gae.22",
)

builder(
    name = "builder_24",
    builder_template = ":builder.toml.template",
    buildpacks = BUILDPACKS,
    descriptor = "builder.toml",
    groups = GROUPS,
    image = "gcp/ruby-24",
    stack = "google.24.full",
)
