description = "Unified builder for the Ruby runtime"

[[buildpacks]]
  id = "google.ruby.functions-framework"
  uri = "functions_framework.tgz"

[[buildpacks]]
  id = "google.config.flex"
  uri = "flex.tgz"

[[buildpacks]]
  id = "google.utils.archive-source"
  uri = "archive_source.tgz"

[[buildpacks]]
  id = "google.ruby.appengine-validation"
  uri = "appengine_validation.tgz"

[[buildpacks]]
  id = "google.ruby.appengine"
  uri = "appengine.tgz"

[[buildpacks]]
  id = "google.ruby.flex-entrypoint"
  uri = "flex_entrypoint.tgz"

[[buildpacks]]
  id = "google.config.entrypoint"
  uri = "entrypoint.tgz"

[[buildpacks]]
  id = "google.ruby.runtime"
  uri = "runtime.tgz"

[[buildpacks]]
  id = "google.ruby.rubygems"
  uri = "rubygems.tgz"

[[buildpacks]]
  id = "google.ruby.bundle"
  uri = "bundle.tgz"

[[buildpacks]]
  id = "google.ruby.rails"
  uri = "rails.tgz"

[[buildpacks]]
  id = "google.nodejs.runtime"
  uri = "nodejs/runtime.tgz"

[[buildpacks]]
  id = "google.ruby.missing-entrypoint"
  uri = "missing_entrypoint.tgz"

[[buildpacks]]
  id = "google.utils.label-image"
  uri = "label_image.tgz"

# The GAE Flex order group.
[[order]]
    [[order.group]]
    id = "google.config.flex"

  [[order.group]]
    id = "google.ruby.runtime"

  [[order.group]]
    id = "google.ruby.bundle"

  [[order.group]]
    id = "google.nodejs.runtime"
    optional = true

  [[order.group]]
    id = "google.ruby.rails"
    optional = true

  [[order.group]]
    id = "google.ruby.flex-entrypoint"

    [[order.group]]
    id = "google.utils.label-image"

# The GAE order group.
[[order]]
  [[order.group]]
    id = "google.ruby.runtime"

  [[order.group]]
    id = "google.ruby.appengine-validation"
    optional = true

  [[order.group]]
  id = "google.ruby.bundle"

  [[order.group]]
    id = "google.nodejs.runtime"
    optional = true

  [[order.group]]
    id = "google.ruby.rails"
    optional = true

  [[order.group]]
    id = "google.ruby.appengine"

  [[order.group]]
    id = "google.utils.label-image"

# The GCF order group
[[order]]
  [[order.group]]
    id = "google.ruby.runtime"

  [[order.group]]
    id = "google.utils.archive-source"
    optional = true

  [[order.group]]
    id = "google.ruby.bundle"

  [[order.group]]
    id = "google.ruby.functions-framework"

  [[order.group]]
    id = "google.utils.label-image"

# The GCP order group.
[[order]]
  [[order.group]]
    id = "google.ruby.runtime"

  [[order.group]]
    id = "google.ruby.rubygems"
    optional = true

  [[order.group]]
    id = "google.ruby.bundle"
    optional = true

  [[order.group]]
    id = "google.nodejs.runtime"
    optional = true

  [[order.group]]
    id = "google.ruby.rails"
    optional = true

  [[order.group]]
    id = "google.config.entrypoint"

  [[order.group]]
    id = "google.utils.label-image"

# This buildpack group will always fail but with a clear message that the
# entrypoint is missing. It must be the last group otherwise projects with
# a single .rb file and no entrypoint will fail
[[order]]
  [[order.group]]
    id = "google.ruby.missing-entrypoint"

[stack]
  id = "${STACK_ID}"
  build-image = "${STACK_BUILD_IMAGE}"
  run-image = "${STACK_RUN_IMAGE}"

[lifecycle]
  version = "0.20.5"