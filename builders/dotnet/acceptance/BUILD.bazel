load("//internal/acceptance:args.bzl", "get_runtime_to_builder_map", "languageargs")
load("//internal/acceptance:defs.bzl", "acceptance_test_argo_source", "acceptance_test_suite", "create_acceptance_versions_dict_file")
load(":args.bzl", "flex_runtime_versions", "gae_runtime_versions", "gcf_runtime_versions", "gcp_runtime_versions")
load(":runtime.bzl", "gcf_runtimes", "version_to_stack")

licenses(["notice"])

exports_files([
    "config.yaml",
    "args.bzl",
])

RUNTIME_TO_BUILDER_MAP = get_runtime_to_builder_map(version_to_stack, "dotnet")

create_acceptance_versions_dict_file(
    name = "gen_acceptance_targets_list",
    file = "acceptance_targets.dict",
    flex_runtime_versions = flex_runtime_versions,
    gae_runtime_versions = gae_runtime_versions,
    gcf_runtime_versions = gcf_runtime_versions,
    gcp_runtime_versions = gcp_runtime_versions,
)

test_suite(
    name = "acceptance_test",
    tests = [
        "flex_test",
        "gae_test",
        "gcf_test",
        "gcp_test",
    ],
)

acceptance_test_suite(
    name = "flex_test",
    srcs = [
        "common_test.go",
        "flex_test.go",
    ],
    builder = "//builders/dotnet:builder.tar",
    rundir = ".",
    runtime_to_builder_map = RUNTIME_TO_BUILDER_MAP,
    testdata = "//builders/testdata/dotnet:flex",
    versions = flex_runtime_versions,
    deps = [
        "@com_github_masterminds_semver//:go_default_library",
    ],
)

acceptance_test_suite(
    name = "gcf_test",
    srcs = [
        "common_test.go",
        "gcf_test.go",
    ],
    argsmap = languageargs(gcf_runtimes, version_to_stack),
    builder = "//builders/dotnet:builder.tar",
    rundir = ".",
    runtime_to_builder_map = RUNTIME_TO_BUILDER_MAP,
    testdata = "//builders/testdata/dotnet:functions",
    versions = gcf_runtime_versions,
    deps = [
        "@com_github_masterminds_semver//:go_default_library",
    ],
)

acceptance_test_suite(
    name = "gcp_test",
    srcs = [
        "common_test.go",
        "gcp_test.go",
    ],
    builder = "//builders/dotnet:builder.tar",
    rundir = ".",
    runtime_to_builder_map = RUNTIME_TO_BUILDER_MAP,
    testdata = "//builders/testdata/dotnet:generic",
    versions = gcp_runtime_versions,
    deps = [
        "@com_github_masterminds_semver//:go_default_library",
    ],
)

# There are no GAE tests for dotnet as there is no dotnet runtime on GAE. However, the language
# test framework expects a `gae_test` target. For that reason this target exists.
acceptance_test_suite(
    name = "gae_test",
    srcs = [
        "common_test.go",
        "gae_test.go",
    ],
    builder = "//builders/dotnet:builder.tar",
    rundir = ".",
    runtime_to_builder_map = RUNTIME_TO_BUILDER_MAP,
    testdata = "//builders/testdata/dotnet:generic",
    # There are no GAE tests for dotnet as there is no dotnet runtime on GAE. However, the language
    # test framework expects a `gae_candidate_test` target. For that reason this target exists.
    versions = gcf_runtime_versions,
    deps = [
        "@com_github_masterminds_semver//:go_default_library",
    ],
)

acceptance_test_suite(
    name = "gae_candidate_test",
    srcs = [
        "common_test.go",
        "gae_test.go",
    ],
    builder = "//builders/dotnet:builder.tar",
    rundir = ".",
    runtime_to_builder_map = RUNTIME_TO_BUILDER_MAP,
    testdata = "//builders/testdata/dotnet:generic",
    # There are no GAE tests for dotnet as there is no dotnet runtime on GAE. However, the language
    # test framework expects a `gae_candidate_test` target. For that reason this target exists.
    versions = gcf_runtime_versions,
    deps = [
        "@com_github_masterminds_semver//:go_default_library",
    ],
)

acceptance_test_argo_source(
    name = "argo_source",
    testdata = "//builders/testdata/dotnet:all_files",
)

exports_files(["runtime.bzl"])
