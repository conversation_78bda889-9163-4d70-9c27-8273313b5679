load("//tools:defs.bzl", "builder")

package(default_visibility = ["//builders/nodejs:__subpackages__"])

licenses(["notice"])

exports_files([
    "builder.toml.template",
])

BUILDPACKS = [
    "//cmd/config/entrypoint:entrypoint.tgz",
    "//cmd/config/flex:flex.tgz",
    "//cmd/nodejs/appengine:appengine.tgz",
    "//cmd/nodejs/functions_framework:functions_framework.tgz",
    "//cmd/nodejs/legacy_worker:legacy_worker.tgz",
    "//cmd/nodejs/npm:npm.tgz",
    "//cmd/nodejs/pnpm:pnpm.tgz",
    "//cmd/nodejs/runtime:runtime.tgz",
    "//cmd/nodejs/yarn:yarn.tgz",
    "//cmd/utils/archive_source:archive_source.tgz",
    "//cmd/utils/label:label_image.tgz",
    "//cmd/nodejs/firebasenextjs:firebasenextjs.tgz",
    "//cmd/nodejs/firebaseangular:firebaseangular.tgz",
    "//cmd/nodejs/firebasebundle:firebasebundle.tgz",
    "//cmd/nodejs/firebasenx:firebasenx.tgz",
    "//cmd/nodejs/turborepo:turborepo.tgz",
]

builder(
    name = "builder",
    builder_template = ":builder.toml.template",
    buildpacks = BUILDPACKS,
    descriptor = "builder.toml",
    image = "gcp/nodejs",
    stack = "google.gae.18",
)

builder(
    name = "builder_22",
    builder_template = ":builder.toml.template",
    buildpacks = BUILDPACKS,
    descriptor = "builder.toml",
    image = "gcp/nodejs-22",
    stack = "google.gae.22",
)

builder(
    name = "builder_24",
    builder_template = ":builder.toml.template",
    buildpacks = BUILDPACKS,
    descriptor = "builder.toml",
    image = "gcp/nodejs-24",
    stack = "google.24.full",
)
