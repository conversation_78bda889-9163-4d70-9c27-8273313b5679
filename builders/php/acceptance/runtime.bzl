"""
Generated bzl file. Do not update manually.
We run acceptance tests only on full stacks.
"""

gae_runtimes = {
    "php55": "5.5",
    "php72": "7.2.34",
    "php73": "7.3.33",
    "php74": "7.4.33",
    "php81": "8.1.33",
    "php82": "8.2.29",
    "php83": "8.3.23",
    "php84": "8.4.10",
}

gcf_runtimes = {
    "php74": "7.4.33",
    "php81": "8.1.33",
    "php82": "8.2.29",
    "php83": "8.3.23",
    "php84": "8.4.10",
}

flex_runtimes = {
    "php74": "7.4.33",
    "php81": "8.1.33",
    "php82": "8.2.29",
    "php83": "8.3.23",
    "php84": "8.4.10",
}

version_to_stack = {
    "php55": "google-18-full",
    "php72": "google-18-full",
    "php73": "google-18-full",
    "php74": "google-18-full",
    "php81": "google-18-full",
    "php82": "google-22-full",
    "php83": "google-22-full",
    "php84": "google-22-full",
}
