# Copyright 2022 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Test container structure using a centigrate image with a custom entrypoint.

# See https://github.com/GoogleContainerTools/container-structure-test for the configuration format.

schemaVersion: '2.0.0'

metadataTest:
  envVars:
    - key: PORT
      value: 8080
  entrypoint: ['/cnb/process/web']
  cmd: []
