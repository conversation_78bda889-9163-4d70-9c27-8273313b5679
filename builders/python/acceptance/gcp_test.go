// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//	http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
package acceptance

import (
	"net/http"
	"testing"

	"github.com/GoogleCloudPlatform/buildpacks/internal/acceptance"
)

const (
	entrypoint              = "google.config.entrypoint"
	pythonFF                = "google.python.functions-framework"
	pythonPIP               = "google.python.pip"
	pythonRuntime           = "google.python.runtime"
	pythonMissingEntrypoint = "google.python.missing-entrypoint"
	pythonWebserver         = "google.python.webserver"
)

func init() {
	acceptance.DefineFlags()
}

func TestAcceptancePython(t *testing.T) {
	imageCtx, cleanup := acceptance.ProvisionImages(t)
	t.Cleanup(cleanup)

	testCases := []acceptance.Test{
		{
			Name:            "entrypoint from procfile web",
			App:             "simple",
			MustUse:         []string{pythonRuntime, pythonPIP, entrypoint},
			EnableCacheTest: true,
		},
		{
			Name:       "entrypoint from procfile custom",
			App:        "simple",
			Path:       "/custom",
			Entrypoint: "custom", // Must match the non-web process in Procfile.
			MustUse:    []string{pythonRuntime, pythonPIP, entrypoint},
		},
		{
			Name:    "entrypoint from env",
			App:     "simple",
			Path:    "/custom",
			Env:     []string{"GOOGLE_ENTRYPOINT=gunicorn -b :8080 custom:app"},
			MustUse: []string{pythonRuntime, pythonPIP, entrypoint},
		},
		{
			Name:    "entrypoint with env var",
			App:     "simple",
			Path:    "/env?want=bar",
			Env:     []string{"GOOGLE_ENTRYPOINT=FOO=bar gunicorn -b :8080 main:app"},
			MustUse: []string{pythonRuntime, pythonPIP, entrypoint},
		},
		{
			Name:    "python with client-side scripts correctly builds as a python app",
			App:     "scripts",
			Env:     []string{"GOOGLE_ENTRYPOINT=gunicorn -b :8080 main:app"},
			MustUse: []string{pythonRuntime, pythonPIP, entrypoint},
		},
		{
			Name:    "python module dependency using a native extension",
			App:     "native_extensions",
			Env:     []string{"GOOGLE_ENTRYPOINT=gunicorn -b :8080 main:app"},
			MustUse: []string{pythonRuntime, pythonPIP, entrypoint},
			// numpy 1.23.1 requires Python 3.8 and <3.12.0.
			VersionInclusionConstraint: ">=3.8.0 <3.12.0",
		},
		{
			Name:    "python module dependency using a native extension for 3.10 and above",
			App:     "native_extensions_above_python310",
			Env:     []string{"GOOGLE_ENTRYPOINT=gunicorn -b :8080 main:app"},
			MustUse: []string{pythonRuntime, pythonPIP, entrypoint},
			// numpy 2.1 needed to support 3.13 only works on python 3.10 and above.
			VersionInclusionConstraint: ">= 3.10.0",
		},
		{
			Name:    "pip vendored dependencies",
			App:     "pip_vendored_dependencies",
			Env:     []string{"GOOGLE_VENDOR_PIP_DEPENDENCIES=package"},
			MustUse: []string{pythonRuntime, pythonPIP, entrypoint},
		},
		{
			Name:                       "fastapi_smart_default_entrypoint_for_3.13_and_above",
			App:                        "fastapi_uvicorn",
			Env:                        []string{"X_GOOGLE_FASTAPI_SMART_DEFAULTS=true"},
			MustUse:                    []string{pythonRuntime, pythonPIP, pythonMissingEntrypoint},
			VersionInclusionConstraint: ">=3.13.0",
		},
		{
			Name:                       "fastapi_app_py_smart_default_entrypoint_for_3.13_and_above",
			App:                        "fastapi_uvicorn_app_py",
			Env:                        []string{"X_GOOGLE_FASTAPI_SMART_DEFAULTS=true"},
			MustUse:                    []string{pythonRuntime, pythonPIP, pythonMissingEntrypoint},
			VersionInclusionConstraint: ">=3.13.0",
		},
		{
			Name:                       "no_fastapi_smart_default_entrypoint_for_3.13_and_below",
			App:                        "fastapi_uvicorn",
			Env:                        []string{"X_GOOGLE_FASTAPI_SMART_DEFAULTS=true"},
			MustUse:                    []string{pythonRuntime, pythonPIP, pythonMissingEntrypoint},
			VersionInclusionConstraint: "<3.13.0",
			MustMatchStatusCode:        http.StatusInternalServerError,
			MustMatch:                  "Internal Server Error",
		},
		{
			Name:                       "gradio_with_python_smart_defaults_for_3.13_and_above",
			App:                        "gradio",
			Env:                        []string{"X_GOOGLE_PYTHON_SMART_DEFAULTS=true"},
			MustUse:                    []string{pythonRuntime, pythonPIP, pythonMissingEntrypoint},
			VersionInclusionConstraint: ">=3.13.0",
		},
		{
			Name:                       "gradio_app_py_with_python_smart_defaults_for_3.13_and_above",
			App:                        "gradio_app_py",
			Env:                        []string{"X_GOOGLE_PYTHON_SMART_DEFAULTS=true"},
			MustUse:                    []string{pythonRuntime, pythonPIP, pythonMissingEntrypoint},
			VersionInclusionConstraint: ">=3.13.0",
		},
		{
			Name:                       "streamlit_with_python_smart_defaults_for_3.13_and_above",
			App:                        "streamlit",
			Env:                        []string{"X_GOOGLE_PYTHON_SMART_DEFAULTS=true"},
			MustUse:                    []string{pythonRuntime, pythonPIP, pythonMissingEntrypoint},
			VersionInclusionConstraint: ">=3.13.0",
			MustMatch:                  "Streamlit",
		},
	}

	for _, tc := range acceptance.FilterTests(t, imageCtx, testCases) {
		tc := tc
		t.Run(tc.Name, func(t *testing.T) {
			// Running these tests in parallel causes the server to run out of disk space.
			// t.Parallel()

			acceptance.TestApp(t, imageCtx, tc)
		})
	}
}

func TestFailuresPython(t *testing.T) {
	imageCtx, cleanup := acceptance.ProvisionImages(t)
	t.Cleanup(cleanup)

	testCases := []acceptance.FailureTest{
		{
			Name:      "bad runtime version",
			App:       "simple",
			Env:       []string{"GOOGLE_RUNTIME_VERSION=BAD_NEWS_BEARS", "GOOGLE_ENTRYPOINT=gunicorn -b :8080 main:app"},
			MustMatch: "invalid Python version specified",
		},
	}

	for _, tc := range testCases {
		tc := tc
		t.Run(tc.Name, func(t *testing.T) {
			// Running these tests in parallel causes the server to run out of disk space.
			// t.Parallel()
			acceptance.TestBuildFailure(t, imageCtx, tc)
		})
	}
}
