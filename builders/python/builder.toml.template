description = "Unified builder all Python runtimes"

[[buildpacks]]
  id = "google.config.entrypoint"
  uri = "entrypoint.tgz"

[[buildpacks]]
  id = "google.config.flex"
  uri = "flex.tgz"

[[buildpacks]]
  id = "google.python.appengine"
  uri = "appengine.tgz"

[[buildpacks]]
  id = "google.python.functions-framework-compat"
  uri = "functions_framework_compat.tgz"

[[buildpacks]]
  id = "google.python.functions-framework"
  uri = "functions_framework.tgz"

[[buildpacks]]
  id = "google.python.missing-entrypoint"
  uri = "missing_entrypoint.tgz"

[[buildpacks]]
  id = "google.python.pip"
  uri = "pip.tgz"

[[buildpacks]]
  id = "google.python.runtime"
  uri = "runtime.tgz"

[[buildpacks]]
  id = "google.python.webserver"
  uri = "webserver.tgz"

[[buildpacks]]
  id = "google.utils.archive-source"
  uri = "archive_source.tgz"

[[buildpacks]]
  id = "google.utils.label-image"
  uri = "label_image.tgz"

[[buildpacks]]
  id = "google.python.link-runtime"
  uri = "link_runtime.tgz"

# GAE Flex
[[order]]
  [[order.group]]
    id = "google.config.flex"

  [[order.group]]
    id = "google.python.runtime"

  [[order.group]]
    id = "google.python.pip"
    optional = true

  [[order.group]]
    id = "google.python.webserver"
    optional = true

  [[order.group]]
    id = "google.config.entrypoint"
    optional = true

  [[order.group]]
    id = "google.utils.label-image"

# Python functions (gcf and gcp).
[[order]]

  # gcf only
  [[order.group]]
    id = "google.utils.archive-source"
    optional = true

  [[order.group]]
    id = "google.python.runtime"

  [[order.group]]
    id = "google.python.functions-framework"

  # gcf and python37 only
  [[order.group]]
    id = "google.python.functions-framework-compat"
    optional = true

  [[order.group]]
    id = "google.python.pip"
    optional = true

  [[order.group]]
    id = "google.utils.label-image"

  [[order.group]]
    id = "google.python.link-runtime"
    optional = true

# Python applications (gae)
[[order]]
  [[order.group]]
    id = "google.python.webserver"
    optional = true

   [[order.group]]
    id = "google.python.runtime"

   [[order.group]]
    id = "google.python.pip"
    optional = true

  [[order.group]]
    id = "google.python.appengine"

  [[order.group]]
    id = "google.utils.label-image"

  [[order.group]]
    id = "google.python.link-runtime"
    optional = true


# Python applications (gcp)
[[order]]
  [[order.group]]
    id = "google.python.runtime"

  [[order.group]]
    id = "google.python.pip"
    optional = true

  # Entrypoint buildpack is required because it cannot be easily inferred.
  [[order.group]]
    id = "google.config.entrypoint"

  [[order.group]]
    id = "google.utils.label-image"

# gcp only
# This buildpack group will attempt to install a webserver if the entrypoint 
# is not provided. And set a default entrypoint if it is not provided given a 
# main.py file exists else it will fail with a message to the user.
[[order]]
  [[order.group]]
    id = "google.python.runtime"

  [[order.group]]
    id = "google.python.webserver"
    optional = true

  [[order.group]]
    id = "google.python.pip"
    optional = true

  [[order.group]]
    id = "google.python.missing-entrypoint"

  [[order.group]]
    id = "google.utils.label-image"

[stack]
  id = "${STACK_ID}"
  build-image = "${STACK_BUILD_IMAGE}"
  run-image = "${STACK_RUN_IMAGE}"

[lifecycle]
  version = "0.20.5"
