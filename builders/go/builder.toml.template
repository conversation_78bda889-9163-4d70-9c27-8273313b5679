description = "Unified builder for the Go runtime"

[[buildpacks]]
  id = "google.utils.archive-source"
  uri = "archive_source.tgz"

[[buildpacks]]
  id = "google.config.entrypoint"
  uri = "entrypoint.tgz"

[[buildpacks]]
  id = "google.config.flex"
  uri = "flex.tgz"

[[buildpacks]]
  id = "google.go.appengine"
  uri = "appengine.tgz"

[[buildpacks]]
  id = "google.go.appengine-gopath"
  uri = "appengine_gopath.tgz"

[[buildpacks]]
  id = "google.go.appengine-gomod"
  uri = "appengine_gomod.tgz"

[[buildpacks]]
  id = "google.go.clear-source"
  uri = "clear_source.tgz"

[[buildpacks]]
  id = "google.go.flex-gomod"
  uri = "flex_gomod.tgz"

[[buildpacks]]
  id = "google.go.legacy-worker"
  uri = "legacy_worker.tgz"

[[buildpacks]]
  id = "google.go.runtime"
  uri = "runtime.tgz"

[[buildpacks]]
  id = "google.go.gomod"
  uri = "gomod.tgz"

[[buildpacks]]
  id = "google.go.build"
  uri = "build.tgz"

[[buildpacks]]
  id = "google.go.gopath"
  uri = "gopath.tgz"

[[buildpacks]]
  id = "google.go.functions-framework"
  uri = "functions_framework.tgz"

[[buildpacks]]
  id = "google.utils.label-image"
  uri = "label_image.tgz"

# GAE Flex with go.mod
[[order]]
  [[order.group]]
    id = "google.go.flex-gomod"
    optional = true

  [[order.group]]
    id = "google.config.flex"

  [[order.group]]
    id = "google.go.runtime"

  [[order.group]]
    id = "google.go.gomod"

  [[order.group]]
    id = "google.go.build"

  [[order.group]]
    id = "google.utils.label-image"

# GAE Flex without go.mod
[[order]]

  [[order.group]]
    id = "google.config.flex"

  [[order.group]]
    id = "google.go.appengine-gopath"

  [[order.group]]
    id = "google.go.runtime"

  [[order.group]]
    id = "google.go.build"

  [[order.group]]
    id = "google.utils.label-image"

[[order]]

  [[order.group]]
    id = "google.go.appengine-gomod"
    optional = true

  [[order.group]]
    id = "google.go.runtime"

  [[order.group]]
    id = "google.go.gomod"

  [[order.group]]
    id = "google.go.build"

  [[order.group]]
    id = "google.go.appengine"

  [[order.group]]
    id = "google.utils.label-image"

[[order]]

  [[order.group]]
    id = "google.go.appengine-gopath"

  [[order.group]]
    id = "google.go.runtime"

  [[order.group]]
    id = "google.go.build"

  [[order.group]]
    id = "google.go.appengine"

  [[order.group]]
    id = "google.utils.label-image"

# The GCF go111 order group. The legacy worker is the "functions-framework"
# buildpack for go111.
[[order]]

  [[order.group]]
    id = "google.utils.archive-source"
    optional = true

  [[order.group]]
    id = "google.go.runtime"

  [[order.group]]
    id = "google.go.legacy-worker"

  [[order.group]]
    id = "google.go.build"

  [[order.group]]
    id = "google.go.clear-source"
    optional = true

  [[order.group]]
    id = "google.utils.label-image"

# The GCF order group.
[[order]]

  [[order.group]]
    id = "google.utils.archive-source"
    optional = true

  [[order.group]]
    id = "google.go.runtime"

  [[order.group]]
    id = "google.go.functions-framework"

  [[order.group]]
    id = "google.go.build"

  [[order.group]]
    id = "google.go.clear-source"
    optional = true

  [[order.group]]
    id = "google.utils.label-image"

[[order]]

  [[order.group]]
    id = "google.go.runtime"

  [[order.group]]
    id = "google.go.gomod"

  [[order.group]]
    id = "google.go.build"

  [[order.group]]
    id = "google.config.entrypoint"
    optional = true

  [[order.group]]
    id = "google.go.clear-source"
    optional = true

  [[order.group]]
    id = "google.utils.label-image"

[[order]]

  [[order.group]]
    id = "google.go.runtime"

  [[order.group]]
    id = "google.go.gopath"
    optional = true

  [[order.group]]
    id = "google.go.build"

  [[order.group]]
    id = "google.config.entrypoint"
    optional = true

  [[order.group]]
    id = "google.go.clear-source"
    optional = true

  [[order.group]]
    id = "google.utils.label-image"

[stack]
  id = "${STACK_ID}"
  build-image = "${STACK_BUILD_IMAGE}"
  run-image = "${STACK_RUN_IMAGE}"

[lifecycle]
  version = "0.20.5"