"""
Generated bzl file. Do not update manually.
We run acceptance tests only on full stacks.
"""

gae_runtimes = {
    "go111": "1.11.13",
    "go112": "1.12.17",
    "go113": "1.13.15",
    "go114": "1.14.15",
    "go115": "1.15.15",
    "go116": "1.16.15",
    "go118": "1.18.10",
    "go119": "1.19.13",
    "go120": "1.20.14",
    "go121": "1.21.13",
    "go122": "1.22.12",
    "go123": "1.23.11",
    "go124": "1.24.5",
    "go125": "1.25rc2",
}

gcf_runtimes = {
    "go111": "1.11.13",
    "go113": "1.13.15",
    "go116": "1.16.15",
    "go118": "1.18.10",
    "go119": "1.19.13",
    "go120": "1.20.14",
    "go121": "1.21.13",
    "go122": "1.22.12",
    "go123": "1.23.11",
    "go124": "1.24.5",
    "go125": "1.25rc2",
}

flex_runtimes = {
    "go118": "1.18.10",
    "go119": "1.19.13",
    "go120": "1.20.14",
    "go121": "1.21.13",
    "go122": "1.22.12",
    "go123": "1.23.11",
    "go124": "1.24.5",
    "go125": "1.25rc2",
}

version_to_stack = {
    "go111": "google-18-full",
    "go112": "google-18-full",
    "go113": "google-18-full",
    "go114": "google-18-full",
    "go115": "google-18-full",
    "go116": "google-18-full",
    "go118": "google-22-full",
    "go119": "google-22-full",
    "go120": "google-22-full",
    "go121": "google-22-full",
    "go122": "google-22-full",
    "go123": "google-22-full",
    "go124": "google-22-full",
    "go125": "google-22-full",
}
