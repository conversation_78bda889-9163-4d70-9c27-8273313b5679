{"type": "project", "require": {"ext-ctype": "*", "ext-iconv": "*", "symfony/console": "5.0.*", "symfony/dotenv": "5.0.*", "symfony/flex": "^1.8.0", "symfony/framework-bundle": "5.0.*", "symfony/yaml": "5.0.*"}, "require-dev": {}, "config": {"preferred-install": {"*": "dist"}, "sort-packages": true, "allow-plugins": {"symfony/flex": true}}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"paragonie/random_compat": "2.*", "symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php71": "*", "symfony/polyfill-php70": "*", "symfony/polyfill-php56": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "5.0.*"}}}