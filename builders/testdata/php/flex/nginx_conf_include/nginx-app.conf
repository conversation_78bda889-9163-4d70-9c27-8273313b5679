rewrite ^/(.*)$ /app.php$uri;

location  ~ ^/app.php {
    error_log stderr;

    fastcgi_pass fast_cgi_app;
    fastcgi_buffering off;
    fastcgi_request_buffering off;
    fastcgi_cache off;
    fastcgi_store off;
    fastcgi_intercept_errors off;

    fastcgi_index app.php;
    fastcgi_split_path_info ^(.+\.php)(.*)$;

    fastcgi_param QUERY_STRING  $query_string;
    fastcgi_param REQUEST_METHOD  $request_method;
    fastcgi_param CONTENT_TYPE  $content_type;
    fastcgi_param CONTENT_LENGTH  $content_length;
    fastcgi_param SCRIPT_NAME $fastcgi_script_name;
    fastcgi_param SCRIPT_FILENAME $document_root/app.php;
    fastcgi_param PATH_INFO $fastcgi_path_info;
    fastcgi_param REQUEST_URI $request_uri;
    fastcgi_param DOCUMENT_URI  $fastcgi_script_name;
    fastcgi_param DOCUMENT_ROOT $document_root;
    fastcgi_param SERVER_PROTOCOL $server_protocol;
    fastcgi_param REQUEST_SCHEME  $scheme;
    if ($http_x_forwarded_proto = 'https') {
      set $https_setting 'on';
    }
    fastcgi_param	HTTPS	$https_setting if_not_empty;

    fastcgi_param	GATEWAY_INTERFACE	CGI/1.1;
    fastcgi_param	REMOTE_ADDR $remote_addr;
    fastcgi_param	REMOTE_PORT $remote_port;
    fastcgi_param	REMOTE_HOST $remote_addr;
    fastcgi_param REMOTE_USER $remote_user;
    fastcgi_param	SERVER_ADDR $server_addr;
    fastcgi_param	SERVER_PORT $server_port;
    fastcgi_param	SERVER_NAME $server_name;
    fastcgi_param X_FORWARDED_FOR $proxy_add_x_forwarded_for;
    fastcgi_param X_FORWARDED_HOST  $http_x_forwarded_host;
    fastcgi_param X_FORWARDED_PROTO $http_x_forwarded_proto;
    fastcgi_param FORWARDED $http_forwarded;
}