# Test data for function builder acceptance tests.

licenses(["notice"])

package(default_visibility = ["//builders:__subpackages__"])

exports_files(
    [
        "appengine",
        "functions",
        "generic",
        "flex",
    ],
)

filegroup(
    name = "all_files",
    srcs = glob(["**"]) + [
        "//builders/testdata/nodejs/appengine:all_files",
    ],
)

filegroup(
    name = "appengine_files",
    srcs = ["//builders/testdata/nodejs/appengine:all_files"],
)

filegroup(
    name = "functions_files",
    srcs = glob(["functions/**"]),
)

filegroup(
    name = "flex_files",
    srcs = glob(["flex/**"]),
)

filegroup(
    name = "generic_files",
    srcs = glob(["generic/**"]),
)
