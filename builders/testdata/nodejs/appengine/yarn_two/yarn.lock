# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 5
  cacheKey: 8

"@arr/every@npm:^1.0.0":
  version: 1.0.1
  resolution: "@arr/every@npm:1.0.1"
  checksum: fc33cd8f3244db7053b89898fc0d1753dd17f40328a443d8640dc15578ed74c59d8e12fe8de87ddb20a2cee578dc0dd9fbb94eb77ef209158fb615bb904b684f
  languageName: node
  linkType: hard

"@polka/url@npm:^0.5.0":
  version: 0.5.0
  resolution: "@polka/url@npm:0.5.0"
  checksum: 3f007adf9c271b28992ebff1df6424e75e7d579493c66969356a9b5dada18480583744dbc28a7467371fa10eb794a5e1dc1f3fcd359c0b5685f4f9c6592cd312
  languageName: node
  linkType: hard

"matchit@npm:^1.0.0":
  version: 1.0.8
  resolution: "matchit@npm:1.0.8"
  dependencies:
    "@arr/every": ^1.0.0
  checksum: c1fb2273218361edde5c2ed6eae3a904b92645454e3037e4e60d87ea0526c9bfb2d2d4707e1ff2efbc6a2ae9994913479e746a2a46ebadafc88a7aaca91017fa
  languageName: node
  linkType: hard

"polka@npm:0.5.2":
  version: 0.5.2
  resolution: "polka@npm:0.5.2"
  dependencies:
    "@polka/url": ^0.5.0
    trouter: ^2.0.1
  checksum: 5f4994e78985e10f77fcccced4d3781ba3f896059f66be6099ee189007b73a9bd4d136f73e4156f731bfe3ead66b1d44448b9be6e57e88fa28b53a6096bd3f11
  languageName: node
  linkType: hard

"root-workspace-0b6124@workspace:.":
  version: 0.0.0-use.local
  resolution: "root-workspace-0b6124@workspace:."
  dependencies:
    polka: 0.5.2
  languageName: unknown
  linkType: soft

"trouter@npm:^2.0.1":
  version: 2.0.1
  resolution: "trouter@npm:2.0.1"
  dependencies:
    matchit: ^1.0.0
  checksum: 4a25e81a132d75e8659a29c4b1f6a91eff06601a78a9d1fc189d525ad0298881ed7db7a82045a3b7d1fcc52cb283d2ca7b79eb908de02088798de36659d5205c
  languageName: node
  linkType: hard
