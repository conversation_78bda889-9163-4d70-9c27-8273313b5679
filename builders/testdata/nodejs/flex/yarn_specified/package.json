{"name": "appengine-hello-world", "description": "Simple Hello World Node.js sample for Google App Engine Flexible Environment.", "version": "0.0.2", "private": true, "license": "Apache-2.0", "author": "Google Inc.", "repository": {"type": "git", "url": "https://github.com/GoogleCloudPlatform/nodejs-docs-samples.git"}, "engines": {"yarn": "1.22.19"}, "scripts": {"start": "node app.js", "test": "mocha --exit test/*.test.js"}, "dependencies": {"express": "^4.17.1"}, "devDependencies": {"mocha": "^10.0.0", "supertest": "^6.0.0"}}