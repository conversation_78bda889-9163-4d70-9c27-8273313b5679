# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


accepts@1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/accepts/-/accepts-1.0.1.tgz#c1e06d613e6246ba874678d6d9b92389b7ce310c"
  integrity sha1-weBtYT5iRrqHRnjW2bkjibfOMQw=
  dependencies:
    mime "~1.2.11"
    negotiator "~0.4.0"

buffer-crc32@0.2.1:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/buffer-crc32/-/buffer-crc32-0.2.1.tgz#be3e5382fc02b6d6324956ac1af98aa98b08534c"
  integrity sha1-vj5TgvwCttYySVasGvmKqYsIU0w=

cookie-signature@1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/cookie-signature/-/cookie-signature-1.0.3.tgz#91cd997cc51fb641595738c69cda020328f50ff9"
  integrity sha1-kc2ZfMUftkFZVzjGnNoCAyj1D/k=

cookie@0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/cookie/-/cookie-0.1.2.tgz#72fec3d24e48a3432073d90c12642005061004b1"
  integrity sha1-cv7D0k5Io0Mgc9kMEmQgBQYQBLE=

debug@0.8.0:
  version "0.8.0"
  resolved "https://registry.yarnpkg.com/debug/-/debug-0.8.0.tgz#0541ea91f0e503fdf0c5eed418a32550234967f0"
  integrity sha1-BUHqkfDlA/3wxe7UGKMlUCNJZ/A=

"debug@>= 0.7.3 < 1":
  version "0.8.1"
  resolved "https://registry.yarnpkg.com/debug/-/debug-0.8.1.tgz#20ff4d26f5e422cb68a1bacbbb61039ad8c1c130"
  integrity sha1-IP9NJvXkIstoobrLu2EDmtjBwTA=

escape-html@1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/escape-html/-/escape-html-1.0.1.tgz#181a286ead397a39a92857cfb1d43052e356bff0"
  integrity sha1-GBoobq05ejmpKFfPsdQwUuNWv/A=

express@4.1.0:
  version "4.1.0"
  resolved "https://registry.yarnpkg.com/express/-/express-4.1.0.tgz#a822be824cf88e8ad67ec5df75d02887de6058b4"
  integrity sha1-qCK+gkz4jorWfsXfddAoh95gWLQ=
  dependencies:
    accepts "1.0.1"
    buffer-crc32 "0.2.1"
    cookie "0.1.2"
    cookie-signature "1.0.3"
    debug ">= 0.7.3 < 1"
    escape-html "1.0.1"
    fresh "0.2.2"
    merge-descriptors "0.0.2"
    methods "0.1.0"
    parseurl "1.0.1"
    path-to-regexp "0.1.2"
    qs "0.6.6"
    range-parser "1.0.0"
    send "0.3.0"
    serve-static "1.1.0"
    type-is "1.1.0"
    utils-merge "1.0.0"

fresh@0.2.2:
  version "0.2.2"
  resolved "https://registry.yarnpkg.com/fresh/-/fresh-0.2.2.tgz#9731dcf5678c7faeb44fb903c4f72df55187fa77"
  integrity sha1-lzHc9WeMf660T7kDxPct9VGH+nc=

fresh@~0.2.1:
  version "0.2.4"
  resolved "https://registry.yarnpkg.com/fresh/-/fresh-0.2.4.tgz#3582499206c9723714190edd74b4604feb4a614c"
  integrity sha1-NYJJkgbJcjcUGQ7ddLRgT+tKYUw=

merge-descriptors@0.0.2:
  version "0.0.2"
  resolved "https://registry.yarnpkg.com/merge-descriptors/-/merge-descriptors-0.0.2.tgz#c36a52a781437513c57275f39dd9d317514ac8c7"
  integrity sha1-w2pSp4FDdRPFcnXzndnTF1FKyMc=

methods@0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/methods/-/methods-0.1.0.tgz#335d429eefd21b7bacf2e9c922a8d2bd14a30e4f"
  integrity sha1-M11Cnu/SG3us8unJIqjSvRSjDk8=

mime@1.2.11, mime@~1.2.11:
  version "1.2.11"
  resolved "https://registry.yarnpkg.com/mime/-/mime-1.2.11.tgz#58203eed86e3a5ef17aed2b7d9ebd47f0a60dd10"
  integrity sha1-WCA+7Ybjpe8XrtK32evUfwpg3RA=

negotiator@~0.4.0:
  version "0.4.9"
  resolved "https://registry.yarnpkg.com/negotiator/-/negotiator-0.4.9.tgz#92e46b6db53c7e421ed64a2bc94f08be7630df3f"
  integrity sha1-kuRrbbU8fkIe1koryU8IvnYw3z8=

parseurl@1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/parseurl/-/parseurl-1.0.1.tgz#2e57dce6efdd37c3518701030944c22bf388b7b4"
  integrity sha1-Llfc5u/dN8NRhwEDCUTCK/OIt7Q=

path-to-regexp@0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/path-to-regexp/-/path-to-regexp-0.1.2.tgz#9b2b151f9cc3018c9eea50ca95729e05781712b4"
  integrity sha1-mysVH5zDAYye6lDKlXKeBXgXErQ=

qs@0.6.6:
  version "0.6.6"
  resolved "https://registry.yarnpkg.com/qs/-/qs-0.6.6.tgz#6e015098ff51968b8a3c819001d5f2c89bc4b107"
  integrity sha1-bgFQmP9RlouKPIGQAdXyyJvEsQc=

range-parser@1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/range-parser/-/range-parser-1.0.0.tgz#a4b264cfe0be5ce36abe3765ac9c2a248746dbc0"
  integrity sha1-pLJkz+C+XONqvjdlrJwqJIdG28A=

range-parser@~1.0.0:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/range-parser/-/range-parser-1.0.3.tgz#6872823535c692e2c2a0103826afd82c2e0ff175"
  integrity sha1-aHKCNTXGkuLCoBA4Jq/YLC4P8XU=

send@0.3.0:
  version "0.3.0"
  resolved "https://registry.yarnpkg.com/send/-/send-0.3.0.tgz#9718324634806fc75bc4f8f5e51f57d9d66606e7"
  integrity sha1-lxgyRjSAb8dbxPj15R9X2dZmBuc=
  dependencies:
    buffer-crc32 "0.2.1"
    debug "0.8.0"
    fresh "~0.2.1"
    mime "1.2.11"
    range-parser "~1.0.0"

serve-static@1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/serve-static/-/serve-static-1.1.0.tgz#454dfa05bb3ddd4e701a8915b83a278aa91c5643"
  integrity sha1-RU36Bbs93U5wGokVuDoniqkcVkM=
  dependencies:
    parseurl "1.0.1"
    send "0.3.0"

type-is@1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/type-is/-/type-is-1.1.0.tgz#d0245ec8b2676668d59dd0cf3255060676a57db6"
  integrity sha1-0CReyLJnZmjVndDPMlUGBnalfbY=
  dependencies:
    mime "~1.2.11"

utils-merge@1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/utils-merge/-/utils-merge-1.0.0.tgz#0294fb922bb9375153541c4f7096231f287c8af8"
  integrity sha1-ApT7kiu5N1FTVBxPcJYjHyh8ivg=
