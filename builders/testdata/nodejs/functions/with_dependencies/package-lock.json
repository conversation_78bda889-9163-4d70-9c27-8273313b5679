{"requires": true, "lockfileVersion": 1, "dependencies": {"buffer-crc32": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.1.tgz", "integrity": "sha1-vj5TgvwCttYySVasGvmKqYsIU0w="}, "bytes": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/bytes/-/bytes-0.2.0.tgz", "integrity": "sha1-qtM+wU49wsp06OfUUfm6BTrU96A="}, "commander": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/commander/-/commander-0.6.1.tgz", "integrity": "sha1-+mihT2qUXVTbvlDYzbMyDp47GgY="}, "connect": {"version": "2.7.10", "resolved": "https://registry.npmjs.org/connect/-/connect-2.7.10.tgz", "integrity": "sha1-oKxZRk1lJAedsCyXFRx5XBtN7mQ=", "requires": {"buffer-crc32": "0.2.1", "bytes": "0.2.0", "cookie": "0.0.5", "cookie-signature": "1.0.1", "debug": "*", "formidable": "1.0.14", "fresh": "0.1.0", "pause": "0.0.1", "qs": "0.6.5", "send": "0.1.0"}, "dependencies": {"cookie": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/cookie/-/cookie-0.0.5.tgz", "integrity": "sha1-+az521frdWjJ/MWWJWt7si4wfIE="}}}, "cookie": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/cookie/-/cookie-0.1.0.tgz", "integrity": "sha1-kOtGndzpBchm3mh+/EMTHYgB+dA="}, "cookie-signature": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.1.tgz", "integrity": "sha1-ROByFIrwHm6OJK+/EmkNaK5pjss="}, "debug": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/debug/-/debug-4.1.1.tgz", "integrity": "sha512-pYA<PERSON>zeRo8J6KPEaJ0VWOh5Pzkbw/RetuzehGM7QRRX5he4fPHx2rdKMB256ehJCkX+XRQm16eZLqLNS8RSZXZw==", "requires": {"ms": "^2.1.1"}}, "express": {"version": "3.2.5", "resolved": "https://registry.npmjs.org/express/-/express-3.2.5.tgz", "integrity": "sha1-0shhNNn6FXO4AE0jxtwNULyO/iA=", "requires": {"buffer-crc32": "0.2.1", "commander": "0.6.1", "connect": "2.7.10", "cookie": "0.1.0", "cookie-signature": "1.0.1", "debug": "*", "fresh": "0.1.0", "methods": "0.0.1", "mkdirp": "0.3.4", "range-parser": "0.0.4", "send": "0.1.0"}}, "formidable": {"version": "1.0.14", "resolved": "https://registry.npmjs.org/formidable/-/formidable-1.0.14.tgz", "integrity": "sha1-Kz9MQRy7X91pXESEPiojUUpDIxo="}, "fresh": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/fresh/-/fresh-0.1.0.tgz", "integrity": "sha1-A+SwF4Qk5MLV0ZpU2IFM3JeTSFA="}, "methods": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/methods/-/methods-0.0.1.tgz", "integrity": "sha1-J3yQ+L7zlwlkWoNxxRw7bGSOBow="}, "mime": {"version": "1.2.6", "resolved": "https://registry.npmjs.org/mime/-/mime-1.2.6.tgz", "integrity": "sha1-sfhsdowCX6h7SAdfFwnyiuryA2U="}, "mkdirp": {"version": "0.3.4", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.3.4.tgz", "integrity": "sha1-+MgdITtymaAx8ZOlfXUqF9L2x9g="}, "ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="}, "pause": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/pause/-/pause-0.0.1.tgz", "integrity": "sha1-HUCLP9t2kjuVQ9lvtMnf1TXZy10="}, "qs": {"version": "0.6.5", "resolved": "https://registry.npmjs.org/qs/-/qs-0.6.5.tgz", "integrity": "sha1-KUsmjksNQlD23eGbO4s0k13/FO8="}, "range-parser": {"version": "0.0.4", "resolved": "https://registry.npmjs.org/range-parser/-/range-parser-0.0.4.tgz", "integrity": "sha1-wEJ//vUcEKy6B4KkbJYC50T/Ygs="}, "send": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/send/-/send-0.1.0.tgz", "integrity": "sha1-z7COvTzsm3/Bo32f+eh1qXHPRkA=", "requires": {"debug": "*", "fresh": "0.1.0", "mime": "1.2.6", "range-parser": "0.0.4"}}}}