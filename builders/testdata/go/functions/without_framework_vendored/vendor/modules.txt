# github.com/GoogleCloudPlatform/functions-framework-go v1.6.1
## explicit
github.com/GoogleCloudPlatform/functions-framework-go/functions
github.com/GoogleCloudPlatform/functions-framework-go/internal/registry
# github.com/cloudevents/sdk-go/v2 v2.6.1
github.com/cloudevents/sdk-go/v2
github.com/cloudevents/sdk-go/v2/binding
github.com/cloudevents/sdk-go/v2/binding/format
github.com/cloudevents/sdk-go/v2/binding/spec
github.com/cloudevents/sdk-go/v2/client
github.com/cloudevents/sdk-go/v2/context
github.com/cloudevents/sdk-go/v2/event
github.com/cloudevents/sdk-go/v2/event/datacodec
github.com/cloudevents/sdk-go/v2/event/datacodec/json
github.com/cloudevents/sdk-go/v2/event/datacodec/text
github.com/cloudevents/sdk-go/v2/event/datacodec/xml
github.com/cloudevents/sdk-go/v2/protocol
github.com/cloudevents/sdk-go/v2/protocol/http
github.com/cloudevents/sdk-go/v2/types
# github.com/google/uuid v1.1.2
github.com/google/uuid
# github.com/json-iterator/go v1.1.10
github.com/json-iterator/go
# github.com/modern-go/concurrent v0.0.0-20180228061459-e0a39a4cb421
github.com/modern-go/concurrent
# github.com/modern-go/reflect2 v0.0.0-20180701023420-4b7aa43c6742
github.com/modern-go/reflect2
# go.uber.org/atomic v1.4.0
go.uber.org/atomic
# go.uber.org/multierr v1.1.0
go.uber.org/multierr
# go.uber.org/zap v1.10.0
go.uber.org/zap
go.uber.org/zap/buffer
go.uber.org/zap/internal/bufferpool
go.uber.org/zap/internal/color
go.uber.org/zap/internal/exit
go.uber.org/zap/zapcore
