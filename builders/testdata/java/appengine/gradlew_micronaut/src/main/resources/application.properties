micronaut.application.name=hello-world
micronaut.router.static-resources.swagger.enabled=true
micronaut.router.static-resources.swagger.paths=classpath:META-INF/swagger
micronaut.router.static-resources.swagger.mapping=/swagger/**

# Uncomment the following lines to enable Consul support
#consul.client.config.enabled=true
#consul.client.config.format=properties
#consul.client.defaultZone=${CONSUL_HOST:localhost}:${CONSUL_PORT:8500}

# Uncomment the following lines to enable zipkin tracing
#tracing.zipkin.enabled=true
#tracing.zipkin.http.url=http://localhost:9411
#tracing.zipkin.sampler.probability=1

# Uncomment the following lines to enable Jaeger tracing
#tracing.jaeger.enabled=true
#tracing.jaeger.sampler.probability=1
