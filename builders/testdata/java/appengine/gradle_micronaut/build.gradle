plugins {
    id "com.gradleup.shadow" version "8.3.8"
    id "application"
}

version "0.1"
group "example"

repositories {
    mavenCentral()
    maven { url "https://jcenter.bintray.com" }
}

configurations {
    // for dependencies that are needed for development only
    developmentOnly 
}

dependencies {
    annotationProcessor platform("io.micronaut:micronaut-bom:$micronautVersion")
    annotationProcessor "io.micronaut:micronaut-inject-java"
    annotationProcessor "io.micronaut:micronaut-validation"
    annotationProcessor "io.micronaut.configuration:micronaut-openapi"
    implementation platform("io.micronaut:micronaut-bom:$micronautVersion")
    implementation "io.micronaut:micronaut-inject"
    implementation "io.swagger.core.v3:swagger-annotations"
    implementation "io.micronaut:micronaut-validation"
    implementation "io.micronaut:micronaut-runtime"
    implementation "io.micronaut:micronaut-http-server-netty"
    implementation "io.micronaut:micronaut-http-client"
    runtimeOnly "ch.qos.logback:logback-classic:1.2.3"
    testAnnotationProcessor platform("io.micronaut:micronaut-bom:$micronautVersion")
    testAnnotationProcessor "io.micronaut:micronaut-inject-java"
    testImplementation platform("io.micronaut:micronaut-bom:$micronautVersion")
    testImplementation "org.junit.jupiter:junit-jupiter-api"
    testImplementation "io.micronaut.test:micronaut-test-junit5"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine"
}

test.classpath += configurations.developmentOnly

application {
    mainClass = "example.Application"
}

// use JUnit 5 platform
test {
    useJUnitPlatform()
}
tasks.withType(JavaCompile){
    options.encoding = "UTF-8"
    options.compilerArgs.add('-parameters')
}

shadowJar {
    mergeServiceFiles()
}

tasks.withType(JavaExec) {
    classpath += configurations.developmentOnly
    jvmArgs('-noverify', '-XX:TieredStopAtLevel=1', '-Dcom.sun.management.jmxremote')
}