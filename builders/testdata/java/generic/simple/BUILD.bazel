load("@rules_java//java:defs.bzl", "java_binary")

licenses(["notice"])

package(default_visibility = ["//builders:__subpackages__"])

exports_files(["Main.java"])

# java_binary runnable jars are in the xxx_deploy.jar target
alias(
    name = "runnablejar",
    actual = "fatjar_deploy.jar",
)

java_binary(
    name = "fatjar",
    srcs = [
        "Main.java",
    ],
    javacopts = ["-source 8 -target 8"],
    main_class = "Main",
)

filegroup(
    name = "all_files",
    srcs = glob(["**"]),
)
