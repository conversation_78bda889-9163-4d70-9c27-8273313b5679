# Test data for function builder acceptance tests.

load("@rules_pkg//pkg:tar.bzl", "pkg_tar")

licenses(["notice"])

package(default_visibility = ["//builders:__subpackages__"])

exports_files(
    [
        "generic",
        "flex",
    ],
)

pkg_tar(
    name = "functions",
    srcs = glob(["functions/**"]) + [
        "//builders/testdata/java/functions/fail_two_jars:fatjar1.jar",
        "//builders/testdata/java/functions/fail_two_jars:fatjar2.jar",
        "//builders/testdata/java/functions/jar:fatjar_deploy.jar",
    ],
    extension = "tgz",
    strip_prefix = "functions",
)

filegroup(
    name = "appengine_files",
    srcs = glob(["appengine/**"]),
)

# The java appengine builder acceptance tests expect the following structure:
#   ./custom_entrypoint/Main.java
#   ./single_jar/fatjar.jar
#   ./<other test cases>
# Note that java/custom_entrypoint is considered a package due to the BUILD file
# and so must be fully-resolved. Likewise the presence of a java/BUILD file
# means that we can't just use java/** here.
pkg_tar(
    name = "appengine",
    srcs = [":appengine_files"],
    extension = "tgz",
    files = {
        "//builders/testdata/java/appengine/custom_entrypoint:Main.java": "custom_entrypoint/Main.java",
        "//builders/testdata/java/appengine/custom_entrypoint:runnablejar": "single_jar/fatjar.jar",
        "//builders/testdata/java/appengine/java11_compat_webapp:appengine-web.xml": "java11_compat_webapp/WEB-INF/appengine-web.xml",
        "//builders/testdata/java/appengine/java11_compat_webapp:web.xml": "java11_compat_webapp/WEB-INF/web.xml",
        "//builders/testdata/java/appengine/java11_compat_webapp:compiledjar": "java11_compat_webapp/WEB-INF/lib/helloservlet.jar",
        "//builders/testdata/java/appengine/java17_compat_webapp:appengine-web.xml": "java17_compat_webapp/WEB-INF/appengine-web.xml",
        "//builders/testdata/java/appengine/java17_compat_webapp:web.xml": "java17_compat_webapp/WEB-INF/web.xml",
        "//builders/testdata/java/appengine/java17_compat_webapp:compiledjar": "java17_compat_webapp/WEB-INF/lib/helloservlet.jar",
    },
    strip_prefix = "appengine",
)

filegroup(
    name = "all_files",
    srcs = glob(["**"]) + [
        "//builders/testdata/java/{}:all_files".format(subpkg)
        for subpkg in subpackages(include = ["**"])
    ],
)

filegroup(
    name = "generic_files",
    srcs = glob([
        "generic/**",
    ]) + [
        "//builders/testdata/java/{}:all_files".format(subpkg)
        for subpkg in subpackages(include = [
            "generic/**",
        ])
    ],
)

filegroup(
    name = "functions_files",
    srcs = ["//builders/testdata/java:functions.tgz"],
)
