# Copyright 2020 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# GCF Python 3.7 legacy worker has additional dependencies available by default
# to user functions. This test ensures that those dependencies can be overriden
# through a user's requirements.txt.
import yarl

def testFunction(request):
  if yarl.__version__ != '1.4.2':
    return 'FAIL: got %s, want %s' % yarl.__version__, '1.4.2'
  return 'PASS'
