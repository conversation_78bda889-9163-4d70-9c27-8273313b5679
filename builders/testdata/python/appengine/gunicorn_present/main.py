# Copyright 2020 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from flask import Flask
import os

app = Flask(__name__)


@app.route('/')
def hello():
  import gunicorn
  if gunicorn.__version__ == '20.1.0':
    return 'PASS'
  return 'FAIL: got version %s, expected version 19.3.0' % gunicorn.__version__


if __name__ == '__main__':
  app.run(port=os.environ['PORT'], debug=True)
