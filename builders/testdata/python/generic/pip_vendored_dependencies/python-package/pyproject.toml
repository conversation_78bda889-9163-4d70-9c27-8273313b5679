[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "sample_pip_dependency"
version = "0.0.1"
authors = [
  { name="<PERSON><PERSON><PERSON>", email="krit<PERSON><PERSON><PERSON>@google.com" },
]
description = "A small example package to test pip vendored deps"
readme = "README.md"
requires-python = ">=3.7"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]

[project.urls]
"Homepage" = "https://github.com/pypa/sampleproject"
"Bug Tracker" = "https://github.com/pypa/sampleproject/issues"
