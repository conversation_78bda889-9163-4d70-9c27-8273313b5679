# Test data for function builder acceptance tests.

licenses(["notice"])

package(default_visibility = ["//builders:__subpackages__"])

exports_files(
    [
        "appengine",
        "functions",
        "generic",
        "flex",
    ],
)

filegroup(
    name = "all_files",
    srcs = glob(["**"]),
)

filegroup(
    name = "appengine_files",
    srcs = glob(["appengine/**"]),
)

filegroup(
    name = "functions_files",
    srcs = glob(["functions/**"]),
)

filegroup(
    name = "flex_files",
    srcs = glob(["flex/**"]),
)

filegroup(
    name = "generic_files",
    srcs = glob(["generic/**"]),
)
