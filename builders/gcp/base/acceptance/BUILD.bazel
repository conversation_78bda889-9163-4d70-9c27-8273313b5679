load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

licenses(["notice"])

exports_files([
    "nodejs_test.go",
    "nodejs_fn_test.go",
    "go_test.go",
    "go_fn_test.go",
    "java_test.go",
    "java_fn_test.go",
    "python_test.go",
    "python_fn_test.go",
    "ruby_test.go",
    "ruby_fn_test.go",
    "cpp_fn_test.go",
    "dotnet_fn_test.go",
    "dotnet_test.go",
    "dart_test.go",
    "php_test.go",
    "acceptance.go",
    "config.yaml",
])

go_library(
    name = "acceptance",
    srcs = ["acceptance.go"],
    importpath = "github.com/GoogleCloudPlatform/buildpacks/" + package_name(),
)

go_test(
    name = "dart_test",
    size = "enormous",
    srcs = ["dart_test.go"],
    args = [
        "-test-data=$(location //builders/testdata/dart:generic)",
        "-structure-test-config=$(location :config.yaml)",
        "-builder-source=$(location //builders/gcp/base:builder.tar)",
        "-builder-prefix=gcpbase-dart-test-",
    ],
    data = [
        ":config.yaml",
        "//builders/gcp/base:builder.tar",
        "//builders/testdata/dart:generic",
    ],
    embed = [":acceptance"],
    rundir = ".",
    tags = [
        "local",
    ],
    deps = ["//internal/acceptance"],
)

go_test(
    name = "dotnet_test",
    size = "enormous",
    srcs = ["dotnet_test.go"],
    args = [
        "-test-data=$(location //builders/testdata/dotnet:generic)",
        "-structure-test-config=$(location :config.yaml)",
        "-builder-source=$(location //builders/gcp/base:builder.tar)",
        "-builder-prefix=gcpbase-dotnet-test-",
    ],
    data = [
        ":config.yaml",
        "//builders/gcp/base:builder.tar",
        "//builders/testdata/dotnet:generic",
    ],
    embed = [":acceptance"],
    rundir = ".",
    tags = [
        "local",
    ],
    deps = ["//internal/acceptance"],
)

go_test(
    name = "go_test",
    size = "enormous",
    srcs = ["go_test.go"],
    args = [
        "-test-data=$(location //builders/testdata/go:generic)",
        "-structure-test-config=$(location :config.yaml)",
        "-builder-source=$(location //builders/gcp/base:builder.tar)",
        "-builder-prefix=gcpbase-go-test-",
    ],
    data = [
        ":config.yaml",
        "//builders/gcp/base:builder.tar",
        "//builders/testdata/go:generic",
    ],
    embed = [":acceptance"],
    rundir = ".",
    tags = [
        "local",
    ],
    deps = ["//internal/acceptance"],
)

go_test(
    name = "java_test",
    size = "enormous",
    srcs = ["java_test.go"],
    args = [
        "-test-data=$(location //builders/testdata/java:generic)",
        "-structure-test-config=$(location :config.yaml)",
        "-builder-source=$(location //builders/gcp/base:builder.tar)",
        "-builder-prefix=gcpbase-java-test-",
    ],
    data = [
        ":config.yaml",
        "//builders/gcp/base:builder.tar",
        "//builders/testdata/java:generic",
    ],
    embed = [":acceptance"],
    rundir = ".",
    tags = [
        "local",
    ],
    deps = ["//internal/acceptance"],
)

go_test(
    name = "nodejs_test",
    size = "enormous",
    srcs = ["nodejs_test.go"],
    args = [
        "-test-data=$(location //builders/testdata/nodejs:generic)",
        "-structure-test-config=$(location :config.yaml)",
        "-builder-source=$(location //builders/gcp/base:builder.tar)",
        "-builder-prefix=gcpbase-nodejs-test-",
    ],
    data = [
        ":config.yaml",
        "//builders/gcp/base:builder.tar",
        "//builders/testdata/nodejs:generic",
    ],
    embed = [":acceptance"],
    rundir = ".",
    tags = [
        "local",
    ],
    deps = ["//internal/acceptance"],
)

go_test(
    name = "php_test",
    size = "enormous",
    srcs = ["php_test.go"],
    args = [
        "-test-data=$(location //builders/testdata/php:generic)",
        "-structure-test-config=$(location :config.yaml)",
        "-builder-source=$(location //builders/gcp/base:builder.tar)",
        "-builder-prefix=gcpbase-php-test-",
    ],
    data = [
        ":config.yaml",
        "//builders/gcp/base:builder.tar",
        "//builders/testdata/php:generic",
    ],
    embed = [":acceptance"],
    rundir = ".",
    tags = [
        "local",
    ],
    deps = ["//internal/acceptance"],
)

go_test(
    name = "python_test",
    size = "enormous",
    srcs = ["python_test.go"],
    args = [
        "-test-data=$(location //builders/testdata/python:generic)",
        "-structure-test-config=$(location :config.yaml)",
        "-builder-source=$(location //builders/gcp/base:builder.tar)",
        "-builder-prefix=gcpbase-python-test-",
    ],
    data = [
        ":config.yaml",
        "//builders/gcp/base:builder.tar",
        "//builders/testdata/python:generic",
    ],
    embed = [":acceptance"],
    rundir = ".",
    tags = [
        "local",
    ],
    deps = ["//internal/acceptance"],
)

go_test(
    name = "ruby_test",
    size = "enormous",
    srcs = ["ruby_test.go"],
    args = [
        "-test-data=$(location //builders/testdata/ruby:generic)",
        "-structure-test-config=$(location :config.yaml)",
        "-builder-source=$(location //builders/gcp/base:builder.tar)",
        "-builder-prefix=gcpbase-ruby-test-",
    ],
    data = [
        ":config.yaml",
        "//builders/gcp/base:builder.tar",
        "//builders/testdata/ruby:generic",
    ],
    embed = [":acceptance"],
    rundir = ".",
    tags = [
        "local",
    ],
    deps = ["//internal/acceptance"],
)

go_test(
    name = "cpp_fn_test",
    size = "enormous",
    srcs = ["cpp_fn_test.go"],
    args = [
        "-test-data=$(location //builders/testdata/cpp:functions)",
        "-structure-test-config=$(location :config.yaml)",
        "-builder-source=$(location //builders/gcp/base:builder.tar)",
        "-builder-prefix=gcpbase-cpp-fn-test-",
    ],
    data = [
        ":config.yaml",
        "//builders/gcp/base:builder.tar",
        "//builders/testdata/cpp:functions",
    ],
    embed = [":acceptance"],
    rundir = ".",
    tags = [
        "local",
    ],
    deps = ["//internal/acceptance"],
)

go_test(
    name = "dotnet_fn_test",
    size = "enormous",
    srcs = ["dotnet_fn_test.go"],
    args = [
        "-test-data=$(location //builders/testdata/dotnet:functions)",
        "-structure-test-config=$(location :config.yaml)",
        "-builder-source=$(location //builders/gcp/base:builder.tar)",
        "-builder-prefix=gcpbase-dotnet-fn-test-",
    ],
    data = [
        ":config.yaml",
        "//builders/gcp/base:builder.tar",
        "//builders/testdata/dotnet:functions",
    ],
    embed = [":acceptance"],
    rundir = ".",
    tags = [
        "local",
    ],
    deps = ["//internal/acceptance"],
)

go_test(
    name = "nodejs_fn_test",
    size = "enormous",
    srcs = ["nodejs_fn_test.go"],
    args = [
        "-test-data=$(location //builders/testdata/nodejs:functions)",
        "-structure-test-config=$(location :config.yaml)",
        "-builder-source=$(location //builders/gcp/base:builder.tar)",
        "-builder-prefix=gcpbase-nodejs-fn-test-",
    ],
    data = [
        ":config.yaml",
        "//builders/gcp/base:builder.tar",
        "//builders/testdata/nodejs:functions",
    ],
    embed = [":acceptance"],
    rundir = ".",
    tags = [
        "local",
    ],
    deps = ["//internal/acceptance"],
)

go_test(
    name = "go_fn_test",
    size = "enormous",
    srcs = ["go_fn_test.go"],
    args = [
        "-test-data=$(location //builders/testdata/go:functions)",
        "-structure-test-config=$(location :config.yaml)",
        "-builder-source=$(location //builders/gcp/base:builder.tar)",
        "-builder-prefix=gcpbase-go-fn-test-",
    ],
    data = [
        ":config.yaml",
        "//builders/gcp/base:builder.tar",
        "//builders/testdata/go:functions",
    ],
    embed = [":acceptance"],
    rundir = ".",
    tags = [
        "local",
    ],
    deps = ["//internal/acceptance"],
)

go_test(
    name = "java_fn_test",
    size = "enormous",
    srcs = ["java_fn_test.go"],
    args = [
        "-test-data=$(location //builders/testdata/java:functions.tgz)",
        "-structure-test-config=$(location :config.yaml)",
        "-builder-source=$(location //builders/gcp/base:builder.tar)",
        "-builder-prefix=gcpbase-java-fn-test-",
    ],
    data = [
        ":config.yaml",
        "//builders/gcp/base:builder.tar",
        "//builders/testdata/java:functions.tgz",
    ],
    embed = [":acceptance"],
    rundir = ".",
    tags = [
        "local",
    ],
    deps = ["//internal/acceptance"],
)

go_test(
    name = "python_fn_test",
    size = "enormous",
    srcs = ["python_fn_test.go"],
    args = [
        "-test-data=$(location //builders/testdata/python:functions)",
        "-structure-test-config=$(location :config.yaml)",
        "-builder-source=$(location //builders/gcp/base:builder.tar)",
        "-builder-prefix=gcpbase-python-fn-test-",
    ],
    data = [
        ":config.yaml",
        "//builders/gcp/base:builder.tar",
        "//builders/testdata/python:functions",
    ],
    embed = [":acceptance"],
    rundir = ".",
    tags = [
        "local",
    ],
    deps = ["//internal/acceptance"],
)

go_test(
    name = "ruby_fn_test",
    size = "enormous",
    srcs = ["ruby_fn_test.go"],
    args = [
        "-test-data=$(location //builders/testdata/ruby:functions)",
        "-structure-test-config=$(location :config.yaml)",
        "-builder-source=$(location //builders/gcp/base:builder.tar)",
        "-builder-prefix=gcpbase-ruby-fn-test-",
    ],
    data = [
        ":config.yaml",
        "//builders/gcp/base:builder.tar",
        "//builders/testdata/ruby:functions",
    ],
    embed = [":acceptance"],
    rundir = ".",
    tags = [
        "local",
    ],
    deps = ["//internal/acceptance"],
)
