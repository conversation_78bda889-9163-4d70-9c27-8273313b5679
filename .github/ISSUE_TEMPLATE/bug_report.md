---
name: Bug report
about: Create a report to help us improve
title: ''
labels: kind/bug
assignees: ''

---

**Describe the bug**
Please provide a  clear and concise description of what the bug is.

If you have a general question about how to use buildpacks, we encourage you to open a [discussion topic instead](https://github.com/GoogleCloudPlatform/buildpacks/discussions)

**Additional context**
How are you using GCP buildpacks?
- [ ] `pack` and the `gcr.io/buildpacks/builder`
- [ ] Cloud Functions
- [ ] Cloud Run
- [ ] Cloud Build
- [ ] App Engine Standard
- [ ] App Engine Flex
- [ ] Firebase App Hosting


**Did this used to work?**
*(Yes/No)*
Was this working before? When did you start noticing these errors?

**What language is your project primarily written in?**
*(Python/Java/Node.js/Go/etc.)*

**Steps To Reproduce**
Steps to reproduce the behavior:
1.  `pack build ...`

**Expected behavior**
A clear and concise description of what you expected to happen.

**Actual behavior**
What actually happened?


If applicable, add screenshots / logs / error messages
